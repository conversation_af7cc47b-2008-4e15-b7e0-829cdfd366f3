import * as React from 'react';
import CheckableTag from './CheckableTag';
import { ConfigConsumerProps } from '../config-provider';
export { CheckableTagProps } from './CheckableTag';
export interface TagProps extends React.HTMLAttributes<HTMLSpanElement> {
    prefixCls?: string;
    className?: string;
    color?: string;
    closable?: boolean;
    visible?: boolean;
    onClose?: Function;
    afterClose?: Function;
    style?: React.CSSProperties;
}
interface TagState {
    visible: boolean;
}
declare class Tag extends React.Component<TagProps, TagState> {
    static CheckableTag: typeof CheckableTag;
    static defaultProps: {
        closable: boolean;
    };
    static getDerivedStateFromProps(nextProps: TagProps): {
        visible: boolean | undefined;
    } | null;
    state: {
        visible: boolean;
    };
    constructor(props: TagProps);
    getTagStyle(): {
        backgroundColor: string | undefined;
    } | {
        alignContent?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "space-around" | "space-between" | "space-evenly" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | undefined;
        alignItems?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | "self-end" | "self-start" | undefined;
        alignSelf?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | "self-end" | "self-start" | undefined;
        animationDelay?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        animationDirection?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "alternate" | "alternate-reverse" | "reverse" | undefined;
        animationDuration?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        animationFillMode?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "backwards" | "both" | "forwards" | undefined;
        animationIterationCount?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "infinite" | (number & {}) | undefined;
        animationName?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        animationPlayState?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "paused" | "running" | undefined;
        animationTimingFunction?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        appearance?: "button" | "meter" | "textarea" | "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "checkbox" | "listbox" | "menulist" | "progress-bar" | "push-button" | "radio" | "searchfield" | "slider-horizontal" | "square-button" | "menulist-button" | "textfield" | undefined;
        aspectRatio?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | undefined;
        backdropFilter?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        backfaceVisibility?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "visible" | undefined;
        backgroundAttachment?: "inherit" | "scroll" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "fixed" | "local" | undefined;
        backgroundBlendMode?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "color" | "color-burn" | "color-dodge" | "darken" | "difference" | "exclusion" | "hard-light" | "hue" | "lighten" | "luminosity" | "multiply" | "overlay" | "saturation" | "screen" | "soft-light" | undefined;
        backgroundClip?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | undefined;
        backgroundColor: string | (string & {}) | undefined;
        backgroundImage?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        backgroundOrigin?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | undefined;
        backgroundPosition?: string | number | (string & {}) | undefined;
        backgroundPositionX?: string | number | (string & {}) | undefined;
        backgroundPositionY?: string | number | (string & {}) | undefined;
        backgroundRepeat?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "no-repeat" | "repeat" | "repeat-x" | "repeat-y" | "round" | "space" | undefined;
        backgroundSize?: string | number | (string & {}) | undefined;
        blockOverflow?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "clip" | "ellipsis" | undefined;
        blockSize?: string | number | (string & {}) | undefined;
        borderBlockColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        borderBlockEndColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        borderBlockEndStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        borderBlockEndWidth?: string | number | undefined;
        borderBlockStartColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        borderBlockStartStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        borderBlockStartWidth?: string | number | undefined;
        borderBlockStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        borderBlockWidth?: string | number | undefined;
        borderBottomColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        borderBottomLeftRadius?: string | number | (string & {}) | undefined;
        borderBottomRightRadius?: string | number | (string & {}) | undefined;
        borderBottomStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        borderBottomWidth?: string | number | undefined;
        borderCollapse?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "collapse" | "separate" | undefined;
        borderEndEndRadius?: string | number | (string & {}) | undefined;
        borderEndStartRadius?: string | number | (string & {}) | undefined;
        borderImageOutset?: string | number | (string & {}) | (number & {}) | undefined;
        borderImageRepeat?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | "repeat" | "round" | "space" | undefined;
        borderImageSlice?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        borderImageSource?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        borderImageWidth?: string | number | (string & {}) | (number & {}) | undefined;
        borderInlineColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        borderInlineEndColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        borderInlineEndStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        borderInlineEndWidth?: string | number | undefined;
        borderInlineStartColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        borderInlineStartStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        borderInlineStartWidth?: string | number | undefined;
        borderInlineStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        borderInlineWidth?: string | number | undefined;
        borderLeftColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        borderLeftStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        borderLeftWidth?: string | number | undefined;
        borderRightColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        borderRightStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        borderRightWidth?: string | number | undefined;
        borderSpacing?: string | number | (string & {}) | undefined;
        borderStartEndRadius?: string | number | (string & {}) | undefined;
        borderStartStartRadius?: string | number | (string & {}) | undefined;
        borderTopColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        borderTopLeftRadius?: string | number | (string & {}) | undefined;
        borderTopRightRadius?: string | number | (string & {}) | undefined;
        borderTopStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        borderTopWidth?: string | number | undefined;
        bottom?: string | number | (string & {}) | undefined;
        boxDecorationBreak?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "clone" | "slice" | undefined;
        boxShadow?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        boxSizing?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | undefined;
        breakAfter?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "left" | "right" | "all" | "always" | "avoid" | "avoid-column" | "avoid-page" | "avoid-region" | "column" | "page" | "recto" | "region" | "verso" | undefined;
        breakBefore?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "left" | "right" | "all" | "always" | "avoid" | "avoid-column" | "avoid-page" | "avoid-region" | "column" | "page" | "recto" | "region" | "verso" | undefined;
        breakInside?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "avoid" | "avoid-column" | "avoid-page" | "avoid-region" | undefined;
        captionSide?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "bottom" | "top" | "block-end" | "block-start" | "inline-end" | "inline-start" | undefined;
        caretColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        clear?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "both" | "left" | "right" | "inline-end" | "inline-start" | undefined;
        clipPath?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | "fill-box" | "margin-box" | "stroke-box" | "view-box" | undefined;
        color?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        colorAdjust?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "economy" | "exact" | undefined;
        columnCount?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        columnFill?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "balance" | undefined;
        columnGap?: string | number | (string & {}) | undefined;
        columnRuleColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        columnRuleStyle?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        columnRuleWidth?: string | number | (string & {}) | undefined;
        columnSpan?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "all" | undefined;
        columnWidth?: string | number | undefined;
        contain?: "style" | "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "content" | "layout" | "paint" | "size" | "strict" | undefined;
        content?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "close-quote" | "no-close-quote" | "no-open-quote" | "open-quote" | "contents" | undefined;
        counterIncrement?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        counterReset?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        counterSet?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        cursor?: "progress" | "text" | "none" | "inherit" | "default" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "-moz-grab" | "-webkit-grab" | "alias" | "all-scroll" | "cell" | "col-resize" | "context-menu" | "copy" | "crosshair" | "e-resize" | "ew-resize" | "grab" | "grabbing" | "help" | "move" | "n-resize" | "ne-resize" | "nesw-resize" | "no-drop" | "not-allowed" | "ns-resize" | "nw-resize" | "nwse-resize" | "pointer" | "row-resize" | "s-resize" | "se-resize" | "sw-resize" | "vertical-text" | "w-resize" | "wait" | "zoom-in" | "zoom-out" | undefined;
        direction?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "ltr" | "rtl" | undefined;
        display?: "ruby" | "table" | "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "contents" | "block" | "inline" | "run-in" | "-ms-flexbox" | "-ms-grid" | "-webkit-flex" | "flex" | "flow" | "flow-root" | "grid" | "ruby-base" | "ruby-base-container" | "ruby-text" | "ruby-text-container" | "table-caption" | "table-cell" | "table-column" | "table-column-group" | "table-footer-group" | "table-header-group" | "table-row" | "table-row-group" | "-ms-inline-flexbox" | "-ms-inline-grid" | "-webkit-inline-flex" | "inline-block" | "inline-flex" | "inline-grid" | "inline-list-item" | "inline-table" | "list-item" | undefined;
        emptyCells?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hide" | "show" | undefined;
        filter?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        flexBasis?: string | number | (string & {}) | undefined;
        flexDirection?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "column" | "column-reverse" | "row" | "row-reverse" | undefined;
        flexGrow?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        flexShrink?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        flexWrap?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "nowrap" | "wrap" | "wrap-reverse" | undefined;
        float?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "left" | "right" | "inline-end" | "inline-start" | undefined;
        fontFamily?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "cursive" | "fantasy" | "monospace" | "sans-serif" | "serif" | undefined;
        fontFeatureSettings?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | undefined;
        fontKerning?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "normal" | undefined;
        fontLanguageOverride?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | undefined;
        fontOpticalSizing?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | undefined;
        fontSize?: string | number | (string & {}) | undefined;
        fontSizeAdjust?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        fontSmooth?: string | number | undefined;
        fontStretch?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "condensed" | "expanded" | "extra-condensed" | "extra-expanded" | "semi-condensed" | "semi-expanded" | "ultra-condensed" | "ultra-expanded" | undefined;
        fontStyle?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "italic" | "oblique" | undefined;
        fontSynthesis?: "style" | "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "weight" | undefined;
        fontVariant?: "ruby" | "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "jis04" | "jis78" | "jis83" | "jis90" | "simplified" | "traditional" | "all-petite-caps" | "all-small-caps" | "common-ligatures" | "contextual" | "diagonal-fractions" | "discretionary-ligatures" | "full-width" | "historical-forms" | "historical-ligatures" | "lining-nums" | "no-common-ligatures" | "no-contextual" | "no-discretionary-ligatures" | "no-historical-ligatures" | "oldstyle-nums" | "ordinal" | "petite-caps" | "proportional-nums" | "proportional-width" | "slashed-zero" | "small-caps" | "stacked-fractions" | "tabular-nums" | "titling-caps" | "unicase" | undefined;
        fontVariantCaps?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "all-petite-caps" | "all-small-caps" | "petite-caps" | "small-caps" | "titling-caps" | "unicase" | undefined;
        fontVariantEastAsian?: "ruby" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "jis04" | "jis78" | "jis83" | "jis90" | "simplified" | "traditional" | "full-width" | "proportional-width" | undefined;
        fontVariantLigatures?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "common-ligatures" | "contextual" | "discretionary-ligatures" | "historical-ligatures" | "no-common-ligatures" | "no-contextual" | "no-discretionary-ligatures" | "no-historical-ligatures" | undefined;
        fontVariantNumeric?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "diagonal-fractions" | "lining-nums" | "oldstyle-nums" | "ordinal" | "proportional-nums" | "slashed-zero" | "stacked-fractions" | "tabular-nums" | undefined;
        fontVariantPosition?: "sub" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "super" | undefined;
        fontVariationSettings?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | undefined;
        fontWeight?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | (number & {}) | "bold" | "bolder" | "lighter" | undefined;
        gridAutoColumns?: string | number | (string & {}) | undefined;
        gridAutoFlow?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "column" | "row" | "dense" | undefined;
        gridAutoRows?: string | number | (string & {}) | undefined;
        gridColumnEnd?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        gridColumnStart?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        gridRowEnd?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        gridRowStart?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        gridTemplateAreas?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        gridTemplateColumns?: string | number | (string & {}) | undefined;
        gridTemplateRows?: string | number | (string & {}) | undefined;
        hangingPunctuation?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "allow-end" | "first" | "force-end" | "last" | undefined;
        height?: string | number | (string & {}) | undefined;
        hyphens?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "manual" | undefined;
        imageOrientation?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "flip" | "from-image" | undefined;
        imageRendering?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "-moz-crisp-edges" | "-webkit-optimize-contrast" | "crisp-edges" | "pixelated" | undefined;
        imageResolution?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "from-image" | undefined;
        initialLetter?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | (number & {}) | undefined;
        inlineSize?: string | number | (string & {}) | undefined;
        inset?: string | number | (string & {}) | undefined;
        insetBlock?: string | number | (string & {}) | undefined;
        insetBlockEnd?: string | number | (string & {}) | undefined;
        insetBlockStart?: string | number | (string & {}) | undefined;
        insetInline?: string | number | (string & {}) | undefined;
        insetInlineEnd?: string | number | (string & {}) | undefined;
        insetInlineStart?: string | number | (string & {}) | undefined;
        isolation?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "isolate" | undefined;
        justifyContent?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "space-around" | "space-between" | "space-evenly" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "normal" | "left" | "right" | undefined;
        justifyItems?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | "self-end" | "self-start" | "left" | "right" | "legacy" | undefined;
        justifySelf?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | "self-end" | "self-start" | "left" | "right" | undefined;
        left?: string | number | (string & {}) | undefined;
        letterSpacing?: string | number | undefined;
        lineBreak?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "normal" | "strict" | "anywhere" | "loose" | undefined;
        lineHeight?: string | number | (string & {}) | (number & {}) | undefined;
        lineHeightStep?: string | number | undefined;
        listStyleImage?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        listStylePosition?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "inside" | "outside" | undefined;
        listStyleType?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        marginBlock?: string | number | (string & {}) | undefined;
        marginBlockEnd?: string | number | (string & {}) | undefined;
        marginBlockStart?: string | number | (string & {}) | undefined;
        marginBottom?: string | number | (string & {}) | undefined;
        marginInline?: string | number | (string & {}) | undefined;
        marginInlineEnd?: string | number | (string & {}) | undefined;
        marginInlineStart?: string | number | (string & {}) | undefined;
        marginLeft?: string | number | (string & {}) | undefined;
        marginRight?: string | number | (string & {}) | undefined;
        marginTop?: string | number | (string & {}) | undefined;
        maskBorderMode?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "alpha" | "luminance" | undefined;
        maskBorderOutset?: string | number | (string & {}) | (number & {}) | undefined;
        maskBorderRepeat?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | "repeat" | "round" | "space" | undefined;
        maskBorderSlice?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        maskBorderSource?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        maskBorderWidth?: string | number | (string & {}) | (number & {}) | undefined;
        maskClip?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | "fill-box" | "margin-box" | "stroke-box" | "view-box" | "no-clip" | undefined;
        maskComposite?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "add" | "exclude" | "intersect" | "subtract" | undefined;
        maskImage?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        maskMode?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "alpha" | "luminance" | "match-source" | undefined;
        maskOrigin?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | "margin-box" | undefined;
        maskPosition?: string | number | (string & {}) | undefined;
        maskRepeat?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "no-repeat" | "repeat" | "repeat-x" | "repeat-y" | "round" | "space" | undefined;
        maskSize?: string | number | (string & {}) | undefined;
        maskType?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "alpha" | "luminance" | undefined;
        mathStyle?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "compact" | undefined;
        maxBlockSize?: string | number | (string & {}) | undefined;
        maxHeight?: string | number | (string & {}) | undefined;
        maxInlineSize?: string | number | (string & {}) | undefined;
        maxLines?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        maxWidth?: string | number | (string & {}) | undefined;
        minBlockSize?: string | number | (string & {}) | undefined;
        minHeight?: string | number | (string & {}) | undefined;
        minInlineSize?: string | number | (string & {}) | undefined;
        minWidth?: string | number | (string & {}) | undefined;
        mixBlendMode?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "color" | "color-burn" | "color-dodge" | "darken" | "difference" | "exclusion" | "hard-light" | "hue" | "lighten" | "luminosity" | "multiply" | "overlay" | "saturation" | "screen" | "soft-light" | undefined;
        motionDistance?: string | number | (string & {}) | undefined;
        motionPath?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | "fill-box" | "margin-box" | "stroke-box" | "view-box" | undefined;
        motionRotation?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "reverse" | undefined;
        objectFit?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "contain" | "cover" | "fill" | "scale-down" | undefined;
        objectPosition?: string | number | (string & {}) | undefined;
        offsetAnchor?: string | number | (string & {}) | undefined;
        offsetDistance?: string | number | (string & {}) | undefined;
        offsetPath?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | "fill-box" | "margin-box" | "stroke-box" | "view-box" | undefined;
        offsetRotate?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "reverse" | undefined;
        offsetRotation?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "reverse" | undefined;
        opacity?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        order?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        orphans?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        outlineColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | "invert" | undefined;
        outlineOffset?: string | number | undefined;
        outlineStyle?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        outlineWidth?: string | number | undefined;
        overflowAnchor?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | undefined;
        overflowBlock?: "inherit" | "scroll" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "hidden" | "visible" | "clip" | undefined;
        overflowClipBox?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "content-box" | "padding-box" | undefined;
        overflowInline?: "inherit" | "scroll" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "hidden" | "visible" | "clip" | undefined;
        overflowWrap?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "anywhere" | "break-word" | undefined;
        overflowX?: "inherit" | "scroll" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "hidden" | "visible" | "clip" | "-moz-hidden-unscrollable" | undefined;
        overflowY?: "inherit" | "scroll" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "hidden" | "visible" | "clip" | "-moz-hidden-unscrollable" | undefined;
        overscrollBehavior?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "contain" | undefined;
        overscrollBehaviorBlock?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "contain" | undefined;
        overscrollBehaviorInline?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "contain" | undefined;
        overscrollBehaviorX?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "contain" | undefined;
        overscrollBehaviorY?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "contain" | undefined;
        paddingBlock?: string | number | (string & {}) | undefined;
        paddingBlockEnd?: string | number | (string & {}) | undefined;
        paddingBlockStart?: string | number | (string & {}) | undefined;
        paddingBottom?: string | number | (string & {}) | undefined;
        paddingInline?: string | number | (string & {}) | undefined;
        paddingInlineEnd?: string | number | (string & {}) | undefined;
        paddingInlineStart?: string | number | (string & {}) | undefined;
        paddingLeft?: string | number | (string & {}) | undefined;
        paddingRight?: string | number | (string & {}) | undefined;
        paddingTop?: string | number | (string & {}) | undefined;
        pageBreakAfter?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "left" | "right" | "always" | "avoid" | "recto" | "verso" | undefined;
        pageBreakBefore?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "left" | "right" | "always" | "avoid" | "recto" | "verso" | undefined;
        pageBreakInside?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "avoid" | undefined;
        paintOrder?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "fill" | "markers" | "stroke" | undefined;
        perspective?: string | number | undefined;
        perspectiveOrigin?: string | number | (string & {}) | undefined;
        placeContent?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "space-around" | "space-between" | "space-evenly" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | undefined;
        pointerEvents?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "visible" | "all" | "fill" | "stroke" | "painted" | "visibleFill" | "visiblePainted" | "visibleStroke" | undefined;
        position?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "-webkit-sticky" | "absolute" | "fixed" | "relative" | "static" | "sticky" | undefined;
        quotes?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | undefined;
        resize?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "both" | "block" | "inline" | "horizontal" | "vertical" | undefined;
        right?: string | number | (string & {}) | undefined;
        rotate?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        rowGap?: string | number | (string & {}) | undefined;
        rubyAlign?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "space-around" | "space-between" | "center" | "start" | undefined;
        rubyMerge?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "collapse" | "separate" | undefined;
        rubyPosition?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "over" | "under" | undefined;
        scale?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        scrollBehavior?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "smooth" | undefined;
        scrollMargin?: string | number | (string & {}) | undefined;
        scrollMarginBlock?: string | number | (string & {}) | undefined;
        scrollMarginBlockEnd?: string | number | undefined;
        scrollMarginBlockStart?: string | number | undefined;
        scrollMarginBottom?: string | number | undefined;
        scrollMarginInline?: string | number | (string & {}) | undefined;
        scrollMarginInlineEnd?: string | number | undefined;
        scrollMarginInlineStart?: string | number | undefined;
        scrollMarginLeft?: string | number | undefined;
        scrollMarginRight?: string | number | undefined;
        scrollMarginTop?: string | number | undefined;
        scrollPadding?: string | number | (string & {}) | undefined;
        scrollPaddingBlock?: string | number | (string & {}) | undefined;
        scrollPaddingBlockEnd?: string | number | (string & {}) | undefined;
        scrollPaddingBlockStart?: string | number | (string & {}) | undefined;
        scrollPaddingBottom?: string | number | (string & {}) | undefined;
        scrollPaddingInline?: string | number | (string & {}) | undefined;
        scrollPaddingInlineEnd?: string | number | (string & {}) | undefined;
        scrollPaddingInlineStart?: string | number | (string & {}) | undefined;
        scrollPaddingLeft?: string | number | (string & {}) | undefined;
        scrollPaddingRight?: string | number | (string & {}) | undefined;
        scrollPaddingTop?: string | number | (string & {}) | undefined;
        scrollSnapAlign?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "center" | "end" | "start" | undefined;
        scrollSnapMargin?: string | number | (string & {}) | undefined;
        scrollSnapMarginBottom?: string | number | undefined;
        scrollSnapMarginLeft?: string | number | undefined;
        scrollSnapMarginRight?: string | number | undefined;
        scrollSnapMarginTop?: string | number | undefined;
        scrollSnapStop?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "always" | undefined;
        scrollSnapType?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "both" | "block" | "inline" | "x" | "y" | undefined;
        scrollbarColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | "dark" | "light" | undefined;
        scrollbarWidth?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "thin" | undefined;
        shapeImageThreshold?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        shapeMargin?: string | number | (string & {}) | undefined;
        shapeOutside?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | "margin-box" | undefined;
        tabSize?: string | number | (number & {}) | undefined;
        tableLayout?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "fixed" | undefined;
        textAlign?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "center" | "end" | "start" | "left" | "right" | "justify" | "match-parent" | undefined;
        textAlignLast?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "center" | "end" | "start" | "left" | "right" | "justify" | undefined;
        textCombineUpright?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "all" | "digits" | undefined;
        textDecorationColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        textDecorationLine?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "blink" | "grammar-error" | "line-through" | "overline" | "spelling-error" | "underline" | undefined;
        textDecorationSkip?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "box-decoration" | "edges" | "leading-spaces" | "objects" | "spaces" | "trailing-spaces" | undefined;
        textDecorationSkipInk?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "all" | undefined;
        textDecorationStyle?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "dashed" | "dotted" | "double" | "solid" | "wavy" | undefined;
        textDecorationThickness?: string | number | (string & {}) | undefined;
        textDecorationWidth?: string | number | (string & {}) | undefined;
        textEmphasisColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        textEmphasisPosition?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        textEmphasisStyle?: "circle" | "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "dot" | "double-circle" | "filled" | "open" | "sesame" | "triangle" | undefined;
        textIndent?: string | number | (string & {}) | undefined;
        textJustify?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "inter-character" | "inter-word" | undefined;
        textOrientation?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "mixed" | "sideways" | "upright" | undefined;
        textOverflow?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "clip" | "ellipsis" | undefined;
        textRendering?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "geometricPrecision" | "optimizeLegibility" | "optimizeSpeed" | undefined;
        textShadow?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        textSizeAdjust?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | undefined;
        textTransform?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "full-width" | "capitalize" | "full-size-kana" | "lowercase" | "uppercase" | undefined;
        textUnderlineOffset?: string | number | (string & {}) | undefined;
        textUnderlinePosition?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "left" | "right" | "under" | "from-font" | undefined;
        top?: string | number | (string & {}) | undefined;
        touchAction?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "-ms-manipulation" | "-ms-none" | "-ms-pinch-zoom" | "manipulation" | "pan-down" | "pan-left" | "pan-right" | "pan-up" | "pan-x" | "pan-y" | "pinch-zoom" | undefined;
        transform?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        transformBox?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "fill-box" | "stroke-box" | "view-box" | undefined;
        transformOrigin?: string | number | (string & {}) | undefined;
        transformStyle?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "flat" | "preserve-3d" | undefined;
        transitionDelay?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        transitionDuration?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        transitionProperty?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "all" | undefined;
        transitionTimingFunction?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        translate?: string | number | (string & {}) | undefined;
        unicodeBidi?: "embed" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "isolate" | "-moz-isolate" | "-moz-isolate-override" | "-moz-plaintext" | "-webkit-isolate" | "bidi-override" | "isolate-override" | "plaintext" | undefined;
        userSelect?: "text" | "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "contain" | "all" | "-moz-none" | "element" | undefined;
        verticalAlign?: string | number | (string & {}) | undefined;
        visibility?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "visible" | "collapse" | undefined;
        whiteSpace?: "pre" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "nowrap" | "-moz-pre-wrap" | "break-spaces" | "pre-line" | "pre-wrap" | undefined;
        widows?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        width?: string | number | (string & {}) | undefined;
        willChange?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "contents" | "scroll-position" | undefined;
        wordBreak?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "break-word" | "break-all" | "keep-all" | undefined;
        wordSpacing?: string | number | (string & {}) | undefined;
        wordWrap?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "break-word" | undefined;
        writingMode?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "horizontal-tb" | "sideways-lr" | "sideways-rl" | "vertical-lr" | "vertical-rl" | undefined;
        zIndex?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        zoom?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | (number & {}) | "reset" | undefined;
        all?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        animation?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "alternate" | "alternate-reverse" | "reverse" | "backwards" | "both" | "forwards" | "infinite" | (number & {}) | "paused" | "running" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        background?: string | number | (string & {}) | undefined;
        border?: string | number | (string & {}) | undefined;
        borderBlock?: string | number | (string & {}) | undefined;
        borderBlockEnd?: string | number | (string & {}) | undefined;
        borderBlockStart?: string | number | (string & {}) | undefined;
        borderBottom?: string | number | (string & {}) | undefined;
        borderColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        borderImage?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | (number & {}) | "repeat" | "round" | "space" | undefined;
        borderInline?: string | number | (string & {}) | undefined;
        borderInlineEnd?: string | number | (string & {}) | undefined;
        borderInlineStart?: string | number | (string & {}) | undefined;
        borderLeft?: string | number | (string & {}) | undefined;
        borderRadius?: string | number | (string & {}) | undefined;
        borderRight?: string | number | (string & {}) | undefined;
        borderStyle?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        borderTop?: string | number | (string & {}) | undefined;
        borderWidth?: string | number | (string & {}) | undefined;
        columnRule?: string | number | (string & {}) | undefined;
        columns?: string | number | (string & {}) | (number & {}) | undefined;
        flex?: string | number | (string & {}) | (number & {}) | undefined;
        flexFlow?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "column" | "column-reverse" | "row" | "row-reverse" | "nowrap" | "wrap" | "wrap-reverse" | undefined;
        font?: "icon" | "caption" | "menu" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "message-box" | "small-caption" | "status-bar" | undefined;
        gap?: string | number | (string & {}) | undefined;
        grid?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        gridArea?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        gridColumn?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        gridRow?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        gridTemplate?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        lineClamp?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        listStyle?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "inside" | "outside" | undefined;
        margin?: string | number | (string & {}) | undefined;
        mask?: string | number | (string & {}) | undefined;
        maskBorder?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | (number & {}) | "repeat" | "round" | "space" | "alpha" | "luminance" | undefined;
        motion?: string | number | (string & {}) | undefined;
        offset?: string | number | (string & {}) | undefined;
        outline?: string | number | (string & {}) | undefined;
        overflow?: "inherit" | "scroll" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "hidden" | "visible" | "clip" | "-moz-hidden-unscrollable" | undefined;
        padding?: string | number | (string & {}) | undefined;
        placeItems?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | "self-end" | "self-start" | undefined;
        placeSelf?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | "self-end" | "self-start" | undefined;
        textDecoration?: string | number | (string & {}) | undefined;
        textEmphasis?: "circle" | "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | "dot" | "double-circle" | "filled" | "open" | "sesame" | "triangle" | undefined;
        transition?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | "all" | undefined;
        MozAnimationDelay?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        MozAnimationDirection?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "alternate" | "alternate-reverse" | "reverse" | undefined;
        MozAnimationDuration?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        MozAnimationFillMode?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "backwards" | "both" | "forwards" | undefined;
        MozAnimationIterationCount?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "infinite" | (number & {}) | undefined;
        MozAnimationName?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        MozAnimationPlayState?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "paused" | "running" | undefined;
        MozAnimationTimingFunction?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        MozAppearance?: "button" | "menuitem" | "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "checkbox" | "listbox" | "menulist" | "radio" | "searchfield" | "menulist-button" | "textfield" | "-moz-mac-unified-toolbar" | "-moz-win-borderless-glass" | "-moz-win-browsertabbar-toolbox" | "-moz-win-communications-toolbox" | "-moz-win-communicationstext" | "-moz-win-exclude-glass" | "-moz-win-glass" | "-moz-win-media-toolbox" | "-moz-win-mediatext" | "-moz-window-button-box" | "-moz-window-button-box-maximized" | "-moz-window-button-close" | "-moz-window-button-maximize" | "-moz-window-button-minimize" | "-moz-window-button-restore" | "-moz-window-frame-bottom" | "-moz-window-frame-left" | "-moz-window-frame-right" | "-moz-window-titlebar" | "-moz-window-titlebar-maximized" | "button-arrow-down" | "button-arrow-next" | "button-arrow-previous" | "button-arrow-up" | "button-bevel" | "button-focus" | "caret" | "checkbox-container" | "checkbox-label" | "checkmenuitem" | "dualbutton" | "groupbox" | "listitem" | "menuarrow" | "menubar" | "menucheckbox" | "menuimage" | "menuitemtext" | "menulist-text" | "menulist-textfield" | "menupopup" | "menuradio" | "menuseparator" | "meterbar" | "meterchunk" | "progressbar" | "progressbar-vertical" | "progresschunk" | "progresschunk-vertical" | "radio-container" | "radio-label" | "radiomenuitem" | "range" | "range-thumb" | "resizer" | "resizerpanel" | "scale-horizontal" | "scale-vertical" | "scalethumb-horizontal" | "scalethumb-vertical" | "scalethumbend" | "scalethumbstart" | "scalethumbtick" | "scrollbarbutton-down" | "scrollbarbutton-left" | "scrollbarbutton-right" | "scrollbarbutton-up" | "scrollbarthumb-horizontal" | "scrollbarthumb-vertical" | "scrollbartrack-horizontal" | "scrollbartrack-vertical" | "separator" | "sheet" | "spinner" | "spinner-downbutton" | "spinner-textfield" | "spinner-upbutton" | "splitter" | "statusbar" | "statusbarpanel" | "tab" | "tab-scroll-arrow-back" | "tab-scroll-arrow-forward" | "tabpanel" | "tabpanels" | "textfield-multiline" | "toolbar" | "toolbarbutton" | "toolbarbutton-dropdown" | "toolbargripper" | "toolbox" | "tooltip" | "treeheader" | "treeheadercell" | "treeheadersortarrow" | "treeitem" | "treeline" | "treetwisty" | "treetwistyopen" | "treeview" | undefined;
        MozBackfaceVisibility?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "visible" | undefined;
        MozBorderBottomColors?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        MozBorderEndColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        MozBorderEndStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        MozBorderEndWidth?: string | number | undefined;
        MozBorderLeftColors?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        MozBorderRightColors?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        MozBorderStartColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        MozBorderStartStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        MozBorderTopColors?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        MozBoxSizing?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | undefined;
        MozColumnCount?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        MozColumnFill?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "balance" | undefined;
        MozColumnGap?: string | number | (string & {}) | undefined;
        MozColumnRuleColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        MozColumnRuleStyle?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        MozColumnRuleWidth?: string | number | (string & {}) | undefined;
        MozColumnWidth?: string | number | undefined;
        MozContextProperties?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "fill" | "stroke" | "fill-opacity" | "stroke-opacity" | undefined;
        MozFontFeatureSettings?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | undefined;
        MozFontLanguageOverride?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | undefined;
        MozHyphens?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "manual" | undefined;
        MozImageRegion?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | undefined;
        MozMarginEnd?: string | number | (string & {}) | undefined;
        MozMarginStart?: string | number | (string & {}) | undefined;
        MozOrient?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "block" | "inline" | "horizontal" | "vertical" | undefined;
        MozOsxFontSmoothing?: string | number | undefined;
        MozPaddingEnd?: string | number | (string & {}) | undefined;
        MozPaddingStart?: string | number | (string & {}) | undefined;
        MozPerspective?: string | number | undefined;
        MozPerspectiveOrigin?: string | number | (string & {}) | undefined;
        MozStackSizing?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "ignore" | "stretch-to-fit" | undefined;
        MozTabSize?: string | number | (number & {}) | undefined;
        MozTextBlink?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "blink" | undefined;
        MozTextSizeAdjust?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | undefined;
        MozTransformOrigin?: string | number | (string & {}) | undefined;
        MozTransformStyle?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "flat" | "preserve-3d" | undefined;
        MozTransitionDelay?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        MozTransitionDuration?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        MozTransitionProperty?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "all" | undefined;
        MozTransitionTimingFunction?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        MozUserFocus?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "ignore" | "select-after" | "select-all" | "select-before" | "select-menu" | "select-same" | undefined;
        MozUserModify?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "read-only" | "read-write" | "write-only" | undefined;
        MozUserSelect?: "text" | "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "contain" | "all" | "-moz-none" | "element" | undefined;
        MozWindowDragging?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "drag" | "no-drag" | undefined;
        MozWindowShadow?: "menu" | "none" | "inherit" | "default" | "-moz-initial" | "initial" | "revert" | "unset" | "sheet" | "tooltip" | undefined;
        msAccelerator?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "true" | "false" | undefined;
        msAlignSelf?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | "self-end" | "self-start" | undefined;
        msBlockProgression?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "bt" | "lr" | "rl" | "tb" | undefined;
        msContentZoomChaining?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "chained" | undefined;
        msContentZoomLimitMax?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msContentZoomLimitMin?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msContentZoomSnapPoints?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msContentZoomSnapType?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "mandatory" | "proximity" | undefined;
        msContentZooming?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "zoom" | undefined;
        msFilter?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msFlexDirection?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "column" | "column-reverse" | "row" | "row-reverse" | undefined;
        msFlexPositive?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        msFlowFrom?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msFlowInto?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msGridColumns?: string | number | (string & {}) | undefined;
        msGridRows?: string | number | (string & {}) | undefined;
        msHighContrastAdjust?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | undefined;
        msHyphenateLimitChars?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        msHyphenateLimitLines?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | "no-limit" | undefined;
        msHyphenateLimitZone?: string | number | (string & {}) | undefined;
        msHyphens?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "manual" | undefined;
        msImeAlign?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "after" | undefined;
        msJustifySelf?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | "self-end" | "self-start" | "left" | "right" | undefined;
        msLineBreak?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "normal" | "strict" | "anywhere" | "loose" | undefined;
        msOrder?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        msOverflowStyle?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "-ms-autohiding-scrollbar" | "scrollbar" | undefined;
        msOverflowX?: "inherit" | "scroll" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "hidden" | "visible" | "clip" | "-moz-hidden-unscrollable" | undefined;
        msOverflowY?: "inherit" | "scroll" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "hidden" | "visible" | "clip" | "-moz-hidden-unscrollable" | undefined;
        msScrollChaining?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "chained" | undefined;
        msScrollLimitXMax?: string | number | undefined;
        msScrollLimitXMin?: string | number | undefined;
        msScrollLimitYMax?: string | number | undefined;
        msScrollLimitYMin?: string | number | undefined;
        msScrollRails?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "railed" | undefined;
        msScrollSnapPointsX?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msScrollSnapPointsY?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msScrollSnapType?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "mandatory" | "proximity" | undefined;
        msScrollTranslation?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "vertical-to-horizontal" | undefined;
        msScrollbar3dlightColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        msScrollbarArrowColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        msScrollbarBaseColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        msScrollbarDarkshadowColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        msScrollbarFaceColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        msScrollbarHighlightColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        msScrollbarShadowColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        msTextAutospace?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "ideograph-alpha" | "ideograph-numeric" | "ideograph-parenthesis" | "ideograph-space" | undefined;
        msTextCombineHorizontal?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "all" | "digits" | undefined;
        msTextOverflow?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "clip" | "ellipsis" | undefined;
        msTouchAction?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "-ms-manipulation" | "-ms-none" | "-ms-pinch-zoom" | "manipulation" | "pan-down" | "pan-left" | "pan-right" | "pan-up" | "pan-x" | "pan-y" | "pinch-zoom" | undefined;
        msTouchSelect?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "grippers" | undefined;
        msTransform?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msTransformOrigin?: string | number | (string & {}) | undefined;
        msTransitionDelay?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msTransitionDuration?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msTransitionProperty?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "all" | undefined;
        msTransitionTimingFunction?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        msUserSelect?: "text" | "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "element" | undefined;
        msWordBreak?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "break-word" | "break-all" | "keep-all" | undefined;
        msWrapFlow?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "end" | "start" | "both" | "clear" | "maximum" | undefined;
        msWrapMargin?: string | number | undefined;
        msWrapThrough?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "wrap" | undefined;
        msWritingMode?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "horizontal-tb" | "sideways-lr" | "sideways-rl" | "vertical-lr" | "vertical-rl" | undefined;
        OObjectFit?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "contain" | "cover" | "fill" | "scale-down" | undefined;
        OObjectPosition?: string | number | (string & {}) | undefined;
        OTabSize?: string | number | (number & {}) | undefined;
        OTextOverflow?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "clip" | "ellipsis" | undefined;
        OTransformOrigin?: string | number | (string & {}) | undefined;
        WebkitAlignContent?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "space-around" | "space-between" | "space-evenly" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | undefined;
        WebkitAlignItems?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | "self-end" | "self-start" | undefined;
        WebkitAlignSelf?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "baseline" | "normal" | "self-end" | "self-start" | undefined;
        WebkitAnimationDelay?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitAnimationDirection?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "alternate" | "alternate-reverse" | "reverse" | undefined;
        WebkitAnimationDuration?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitAnimationFillMode?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "backwards" | "both" | "forwards" | undefined;
        WebkitAnimationIterationCount?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "infinite" | (number & {}) | undefined;
        WebkitAnimationName?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitAnimationPlayState?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "paused" | "running" | undefined;
        WebkitAnimationTimingFunction?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        WebkitAppearance?: "button" | "meter" | "textarea" | "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "checkbox" | "listbox" | "menulist" | "progress-bar" | "push-button" | "radio" | "searchfield" | "slider-horizontal" | "square-button" | "menulist-button" | "textfield" | "button-bevel" | "caret" | "listitem" | "menulist-text" | "menulist-textfield" | "-apple-pay-button" | "default-button" | "inner-spin-button" | "media-controls-background" | "media-controls-fullscreen-background" | "media-current-time-display" | "media-enter-fullscreen-button" | "media-exit-fullscreen-button" | "media-fullscreen-button" | "media-mute-button" | "media-overlay-play-button" | "media-play-button" | "media-seek-back-button" | "media-seek-forward-button" | "media-slider" | "media-sliderthumb" | "media-time-remaining-display" | "media-toggle-closed-captions-button" | "media-volume-slider" | "media-volume-slider-container" | "media-volume-sliderthumb" | "progress-bar-value" | "searchfield-cancel-button" | "searchfield-decoration" | "searchfield-results-button" | "searchfield-results-decoration" | "slider-vertical" | "sliderthumb-horizontal" | "sliderthumb-vertical" | undefined;
        WebkitBackdropFilter?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitBackfaceVisibility?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "visible" | undefined;
        WebkitBackgroundClip?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | undefined;
        WebkitBackgroundOrigin?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | undefined;
        WebkitBackgroundSize?: string | number | (string & {}) | undefined;
        WebkitBorderBeforeColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        WebkitBorderBeforeStyle?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        WebkitBorderBeforeWidth?: string | number | (string & {}) | undefined;
        WebkitBorderBottomLeftRadius?: string | number | (string & {}) | undefined;
        WebkitBorderBottomRightRadius?: string | number | (string & {}) | undefined;
        WebkitBorderImageSlice?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        WebkitBorderTopLeftRadius?: string | number | (string & {}) | undefined;
        WebkitBorderTopRightRadius?: string | number | (string & {}) | undefined;
        WebkitBoxDecorationBreak?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "clone" | "slice" | undefined;
        WebkitBoxReflect?: string | number | (string & {}) | undefined;
        WebkitBoxShadow?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitBoxSizing?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | undefined;
        WebkitClipPath?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | "fill-box" | "margin-box" | "stroke-box" | "view-box" | undefined;
        WebkitColumnCount?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        WebkitColumnFill?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "balance" | undefined;
        WebkitColumnGap?: string | number | (string & {}) | undefined;
        WebkitColumnRuleColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        WebkitColumnRuleStyle?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        WebkitColumnRuleWidth?: string | number | (string & {}) | undefined;
        WebkitColumnSpan?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "all" | undefined;
        WebkitColumnWidth?: string | number | undefined;
        WebkitFilter?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitFlexBasis?: string | number | (string & {}) | undefined;
        WebkitFlexDirection?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "column" | "column-reverse" | "row" | "row-reverse" | undefined;
        WebkitFlexGrow?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        WebkitFlexShrink?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        WebkitFlexWrap?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "nowrap" | "wrap" | "wrap-reverse" | undefined;
        WebkitFontFeatureSettings?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | undefined;
        WebkitFontKerning?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "normal" | undefined;
        WebkitFontSmoothing?: string | number | undefined;
        WebkitFontVariantLigatures?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "common-ligatures" | "contextual" | "discretionary-ligatures" | "historical-ligatures" | "no-common-ligatures" | "no-contextual" | "no-discretionary-ligatures" | "no-historical-ligatures" | undefined;
        WebkitHyphens?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "manual" | undefined;
        WebkitJustifyContent?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "space-around" | "space-between" | "space-evenly" | "stretch" | "center" | "end" | "flex-end" | "flex-start" | "start" | "normal" | "left" | "right" | undefined;
        WebkitLineBreak?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "normal" | "strict" | "anywhere" | "loose" | undefined;
        WebkitLineClamp?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        WebkitMarginEnd?: string | number | (string & {}) | undefined;
        WebkitMarginStart?: string | number | (string & {}) | undefined;
        WebkitMaskAttachment?: "inherit" | "scroll" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "fixed" | "local" | undefined;
        WebkitMaskClip?: "text" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | "content" | "border" | "padding" | undefined;
        WebkitMaskComposite?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "copy" | "clear" | "destination-atop" | "destination-in" | "destination-out" | "destination-over" | "source-atop" | "source-in" | "source-out" | "source-over" | "xor" | undefined;
        WebkitMaskImage?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitMaskOrigin?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | "content" | "border" | "padding" | undefined;
        WebkitMaskPosition?: string | number | (string & {}) | undefined;
        WebkitMaskPositionX?: string | number | (string & {}) | undefined;
        WebkitMaskPositionY?: string | number | (string & {}) | undefined;
        WebkitMaskRepeat?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "no-repeat" | "repeat" | "repeat-x" | "repeat-y" | "round" | "space" | undefined;
        WebkitMaskRepeatX?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "no-repeat" | "repeat" | "round" | "space" | undefined;
        WebkitMaskRepeatY?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "no-repeat" | "repeat" | "round" | "space" | undefined;
        WebkitMaskSize?: string | number | (string & {}) | undefined;
        WebkitMaxInlineSize?: string | number | (string & {}) | undefined;
        WebkitOrder?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        WebkitOverflowScrolling?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "touch" | undefined;
        WebkitPaddingEnd?: string | number | (string & {}) | undefined;
        WebkitPaddingStart?: string | number | (string & {}) | undefined;
        WebkitPerspective?: string | number | undefined;
        WebkitPerspectiveOrigin?: string | number | (string & {}) | undefined;
        WebkitPrintColorAdjust?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "economy" | "exact" | undefined;
        WebkitScrollSnapType?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "both" | "block" | "inline" | "x" | "y" | undefined;
        WebkitShapeMargin?: string | number | (string & {}) | undefined;
        WebkitTapHighlightColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        WebkitTextCombine?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "all" | "digits" | undefined;
        WebkitTextDecorationColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        WebkitTextDecorationLine?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "blink" | "grammar-error" | "line-through" | "overline" | "spelling-error" | "underline" | undefined;
        WebkitTextDecorationSkip?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "box-decoration" | "edges" | "leading-spaces" | "objects" | "spaces" | "trailing-spaces" | undefined;
        WebkitTextDecorationStyle?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "dashed" | "dotted" | "double" | "solid" | "wavy" | undefined;
        WebkitTextEmphasisColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        WebkitTextEmphasisPosition?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitTextEmphasisStyle?: "circle" | "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "dot" | "double-circle" | "filled" | "open" | "sesame" | "triangle" | undefined;
        WebkitTextFillColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        WebkitTextOrientation?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "mixed" | "sideways" | "upright" | undefined;
        WebkitTextSizeAdjust?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | undefined;
        WebkitTextStrokeColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        WebkitTextStrokeWidth?: string | number | undefined;
        WebkitTextUnderlinePosition?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "left" | "right" | "under" | "from-font" | undefined;
        WebkitTouchCallout?: "none" | "inherit" | "default" | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitTransform?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitTransformOrigin?: string | number | (string & {}) | undefined;
        WebkitTransformStyle?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "flat" | "preserve-3d" | undefined;
        WebkitTransitionDelay?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitTransitionDuration?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitTransitionProperty?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "all" | undefined;
        WebkitTransitionTimingFunction?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        WebkitUserModify?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "read-only" | "read-write" | "read-write-plaintext-only" | undefined;
        WebkitUserSelect?: "text" | "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "contain" | "all" | "-moz-none" | "element" | undefined;
        WebkitWritingMode?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "horizontal-tb" | "sideways-lr" | "sideways-rl" | "vertical-lr" | "vertical-rl" | undefined;
        MozAnimation?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "alternate" | "alternate-reverse" | "reverse" | "backwards" | "both" | "forwards" | "infinite" | (number & {}) | "paused" | "running" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        MozBorderImage?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | (number & {}) | "repeat" | "round" | "space" | undefined;
        MozColumnRule?: string | number | (string & {}) | undefined;
        MozColumns?: string | number | (string & {}) | (number & {}) | undefined;
        MozTransition?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | "all" | undefined;
        msContentZoomLimit?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msContentZoomSnap?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "mandatory" | "proximity" | undefined;
        msFlex?: string | number | (string & {}) | (number & {}) | undefined;
        msScrollLimit?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msScrollSnapX?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msScrollSnapY?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        msTransition?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | "all" | undefined;
        WebkitAnimation?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "alternate" | "alternate-reverse" | "reverse" | "backwards" | "both" | "forwards" | "infinite" | (number & {}) | "paused" | "running" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        WebkitBorderBefore?: string | number | (string & {}) | undefined;
        WebkitBorderImage?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | (number & {}) | "repeat" | "round" | "space" | undefined;
        WebkitBorderRadius?: string | number | (string & {}) | undefined;
        WebkitColumnRule?: string | number | (string & {}) | undefined;
        WebkitColumns?: string | number | (string & {}) | (number & {}) | undefined;
        WebkitFlex?: string | number | (string & {}) | (number & {}) | undefined;
        WebkitFlexFlow?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "column" | "column-reverse" | "row" | "row-reverse" | "nowrap" | "wrap" | "wrap-reverse" | undefined;
        WebkitMask?: string | number | (string & {}) | undefined;
        WebkitTextEmphasis?: "circle" | "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | "dot" | "double-circle" | "filled" | "open" | "sesame" | "triangle" | undefined;
        WebkitTextStroke?: string | number | (string & {}) | undefined;
        WebkitTransition?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | "all" | undefined;
        boxAlign?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | "center" | "end" | "start" | "baseline" | undefined;
        boxDirection?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "reverse" | undefined;
        boxFlex?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        boxFlexGroup?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        boxLines?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "multiple" | "single" | undefined;
        boxOrdinalGroup?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        boxOrient?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "horizontal" | "vertical" | "block-axis" | "inline-axis" | undefined;
        boxPack?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "center" | "end" | "start" | "justify" | undefined;
        clip?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | undefined;
        fontVariantAlternates?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "historical-forms" | undefined;
        gridColumnGap?: string | number | (string & {}) | undefined;
        gridGap?: string | number | (string & {}) | undefined;
        gridRowGap?: string | number | (string & {}) | undefined;
        imeMode?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "disabled" | "normal" | "active" | "inactive" | undefined;
        offsetBlock?: string | number | (string & {}) | undefined;
        offsetBlockEnd?: string | number | (string & {}) | undefined;
        offsetBlockStart?: string | number | (string & {}) | undefined;
        offsetInline?: string | number | (string & {}) | undefined;
        offsetInlineEnd?: string | number | (string & {}) | undefined;
        offsetInlineStart?: string | number | (string & {}) | undefined;
        scrollSnapCoordinate?: string | number | (string & {}) | undefined;
        scrollSnapDestination?: string | number | (string & {}) | undefined;
        scrollSnapPointsX?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        scrollSnapPointsY?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        scrollSnapTypeX?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "mandatory" | "proximity" | undefined;
        scrollSnapTypeY?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "mandatory" | "proximity" | undefined;
        scrollbarTrackColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        textCombineHorizontal?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "all" | "digits" | undefined;
        KhtmlBoxAlign?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | "center" | "end" | "start" | "baseline" | undefined;
        KhtmlBoxDirection?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "reverse" | undefined;
        KhtmlBoxFlex?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        KhtmlBoxFlexGroup?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        KhtmlBoxLines?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "multiple" | "single" | undefined;
        KhtmlBoxOrdinalGroup?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        KhtmlBoxOrient?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "horizontal" | "vertical" | "block-axis" | "inline-axis" | undefined;
        KhtmlBoxPack?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "center" | "end" | "start" | "justify" | undefined;
        KhtmlLineBreak?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "normal" | "strict" | "anywhere" | "loose" | undefined;
        KhtmlOpacity?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        KhtmlUserSelect?: "text" | "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "contain" | "all" | "-moz-none" | "element" | undefined;
        MozBackgroundClip?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | undefined;
        MozBackgroundInlinePolicy?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "clone" | "slice" | undefined;
        MozBackgroundOrigin?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | undefined;
        MozBackgroundSize?: string | number | (string & {}) | undefined;
        MozBinding?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        MozBorderRadius?: string | number | (string & {}) | undefined;
        MozBorderRadiusBottomleft?: string | number | (string & {}) | undefined;
        MozBorderRadiusBottomright?: string | number | (string & {}) | undefined;
        MozBorderRadiusTopleft?: string | number | (string & {}) | undefined;
        MozBorderRadiusTopright?: string | number | (string & {}) | undefined;
        MozBoxAlign?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | "center" | "end" | "start" | "baseline" | undefined;
        MozBoxDirection?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "reverse" | undefined;
        MozBoxFlex?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        MozBoxOrdinalGroup?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        MozBoxOrient?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "horizontal" | "vertical" | "block-axis" | "inline-axis" | undefined;
        MozBoxPack?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "center" | "end" | "start" | "justify" | undefined;
        MozBoxShadow?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        MozFloatEdge?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "border-box" | "content-box" | "padding-box" | "margin-box" | undefined;
        MozForceBrokenImageIcon?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        MozOpacity?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        MozOutline?: string | number | (string & {}) | undefined;
        MozOutlineColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | "invert" | undefined;
        MozOutlineRadius?: string | number | (string & {}) | undefined;
        MozOutlineRadiusBottomleft?: string | number | (string & {}) | undefined;
        MozOutlineRadiusBottomright?: string | number | (string & {}) | undefined;
        MozOutlineRadiusTopleft?: string | number | (string & {}) | undefined;
        MozOutlineRadiusTopright?: string | number | (string & {}) | undefined;
        MozOutlineStyle?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "hidden" | "dashed" | "dotted" | "double" | "groove" | "inset" | "outset" | "ridge" | "solid" | undefined;
        MozOutlineWidth?: string | number | undefined;
        MozTextAlignLast?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "center" | "end" | "start" | "left" | "right" | "justify" | undefined;
        MozTextDecorationColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        MozTextDecorationLine?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "blink" | "grammar-error" | "line-through" | "overline" | "spelling-error" | "underline" | undefined;
        MozTextDecorationStyle?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "dashed" | "dotted" | "double" | "solid" | "wavy" | undefined;
        MozUserInput?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "disabled" | "enabled" | undefined;
        msImeMode?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "disabled" | "normal" | "active" | "inactive" | undefined;
        msScrollbarTrackColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | undefined;
        OAnimation?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "alternate" | "alternate-reverse" | "reverse" | "backwards" | "both" | "forwards" | "infinite" | (number & {}) | "paused" | "running" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        OAnimationDelay?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        OAnimationDirection?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "alternate" | "alternate-reverse" | "reverse" | undefined;
        OAnimationDuration?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        OAnimationFillMode?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "backwards" | "both" | "forwards" | undefined;
        OAnimationIterationCount?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "infinite" | (number & {}) | undefined;
        OAnimationName?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        OAnimationPlayState?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "paused" | "running" | undefined;
        OAnimationTimingFunction?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        OBackgroundSize?: string | number | (string & {}) | undefined;
        OBorderImage?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | (number & {}) | "repeat" | "round" | "space" | undefined;
        OTransform?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        OTransition?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | "all" | undefined;
        OTransitionDelay?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        OTransitionDuration?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        OTransitionProperty?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "all" | undefined;
        OTransitionTimingFunction?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "ease" | "ease-in" | "ease-in-out" | "ease-out" | "step-end" | "step-start" | "linear" | undefined;
        WebkitBoxAlign?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "stretch" | "center" | "end" | "start" | "baseline" | undefined;
        WebkitBoxDirection?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "normal" | "reverse" | undefined;
        WebkitBoxFlex?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        WebkitBoxFlexGroup?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        WebkitBoxLines?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "multiple" | "single" | undefined;
        WebkitBoxOrdinalGroup?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        WebkitBoxOrient?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "horizontal" | "vertical" | "block-axis" | "inline-axis" | undefined;
        WebkitBoxPack?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "center" | "end" | "start" | "justify" | undefined;
        WebkitScrollSnapPointsX?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        WebkitScrollSnapPointsY?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        alignmentBaseline?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "baseline" | "middle" | "after-edge" | "alphabetic" | "before-edge" | "central" | "hanging" | "ideographic" | "mathematical" | "text-after-edge" | "text-before-edge" | undefined;
        baselineShift?: string | number | (string & {}) | undefined;
        clipRule?: "evenodd" | "nonzero" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        colorInterpolation?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "linearRGB" | "sRGB" | undefined;
        colorRendering?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "optimizeSpeed" | "optimizeQuality" | undefined;
        dominantBaseline?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "middle" | "alphabetic" | "central" | "hanging" | "ideographic" | "mathematical" | "text-after-edge" | "text-before-edge" | "no-change" | "reset-size" | "use-script" | undefined;
        fill?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | "child" | "context-fill" | "context-stroke" | undefined;
        fillOpacity?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        fillRule?: "evenodd" | "nonzero" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        floodColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | "currentColor" | undefined;
        floodOpacity?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        glyphOrientationVertical?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | (number & {}) | undefined;
        lightingColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | "currentColor" | undefined;
        marker?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        markerEnd?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        markerMid?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        markerStart?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | undefined;
        shapeRendering?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "auto" | "geometricPrecision" | "optimizeSpeed" | "crispEdges" | undefined;
        stopColor?: "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | "currentColor" | undefined;
        stopOpacity?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        stroke?: "none" | "inherit" | (string & {}) | "-moz-initial" | "initial" | "revert" | "unset" | "aliceblue" | "antiquewhite" | "aqua" | "aquamarine" | "azure" | "beige" | "bisque" | "black" | "blanchedalmond" | "blue" | "blueviolet" | "brown" | "burlywood" | "cadetblue" | "chartreuse" | "chocolate" | "coral" | "cornflowerblue" | "cornsilk" | "crimson" | "cyan" | "darkblue" | "darkcyan" | "darkgoldenrod" | "darkgray" | "darkgreen" | "darkgrey" | "darkkhaki" | "darkmagenta" | "darkolivegreen" | "darkorange" | "darkorchid" | "darkred" | "darksalmon" | "darkseagreen" | "darkslateblue" | "darkslategray" | "darkslategrey" | "darkturquoise" | "darkviolet" | "deeppink" | "deepskyblue" | "dimgray" | "dimgrey" | "dodgerblue" | "firebrick" | "floralwhite" | "forestgreen" | "fuchsia" | "gainsboro" | "ghostwhite" | "gold" | "goldenrod" | "gray" | "green" | "greenyellow" | "grey" | "honeydew" | "hotpink" | "indianred" | "indigo" | "ivory" | "khaki" | "lavender" | "lavenderblush" | "lawngreen" | "lemonchiffon" | "lightblue" | "lightcoral" | "lightcyan" | "lightgoldenrodyellow" | "lightgray" | "lightgreen" | "lightgrey" | "lightpink" | "lightsalmon" | "lightseagreen" | "lightskyblue" | "lightslategray" | "lightslategrey" | "lightsteelblue" | "lightyellow" | "lime" | "limegreen" | "linen" | "magenta" | "maroon" | "mediumaquamarine" | "mediumblue" | "mediumorchid" | "mediumpurple" | "mediumseagreen" | "mediumslateblue" | "mediumspringgreen" | "mediumturquoise" | "mediumvioletred" | "midnightblue" | "mintcream" | "mistyrose" | "moccasin" | "navajowhite" | "navy" | "oldlace" | "olive" | "olivedrab" | "orange" | "orangered" | "orchid" | "palegoldenrod" | "palegreen" | "paleturquoise" | "palevioletred" | "papayawhip" | "peachpuff" | "peru" | "pink" | "plum" | "powderblue" | "purple" | "rebeccapurple" | "red" | "rosybrown" | "royalblue" | "saddlebrown" | "salmon" | "sandybrown" | "seagreen" | "seashell" | "sienna" | "silver" | "skyblue" | "slateblue" | "slategray" | "slategrey" | "snow" | "springgreen" | "steelblue" | "tan" | "teal" | "thistle" | "tomato" | "transparent" | "turquoise" | "violet" | "wheat" | "white" | "whitesmoke" | "yellow" | "yellowgreen" | "ActiveBorder" | "ActiveCaption" | "AppWorkspace" | "Background" | "ButtonFace" | "ButtonHighlight" | "ButtonShadow" | "ButtonText" | "CaptionText" | "GrayText" | "Highlight" | "HighlightText" | "InactiveBorder" | "InactiveCaption" | "InactiveCaptionText" | "InfoBackground" | "InfoText" | "Menu" | "MenuText" | "Scrollbar" | "ThreeDDarkShadow" | "ThreeDFace" | "ThreeDHighlight" | "ThreeDLightShadow" | "ThreeDShadow" | "Window" | "WindowFrame" | "WindowText" | "currentcolor" | "child" | "context-fill" | "context-stroke" | undefined;
        strokeDasharray?: string | number | (string & {}) | (number & {}) | undefined;
        strokeDashoffset?: string | number | (string & {}) | undefined;
        strokeLinecap?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "round" | "butt" | "square" | undefined;
        strokeLinejoin?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "round" | "bevel" | "miter" | undefined;
        strokeMiterlimit?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        strokeOpacity?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | (number & {}) | undefined;
        strokeWidth?: string | number | (string & {}) | undefined;
        textAnchor?: "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "end" | "start" | "middle" | undefined;
        vectorEffect?: "none" | "inherit" | "-moz-initial" | "initial" | "revert" | "unset" | "non-scaling-stroke" | undefined;
    };
    getTagClassName({ getPrefixCls }: ConfigConsumerProps): string;
    setVisible(visible: boolean, e: React.MouseEvent<HTMLElement>): void;
    handleIconClick: (e: React.MouseEvent<HTMLElement, MouseEvent>) => void;
    isPresetColor(): boolean;
    renderCloseIcon(): JSX.Element | null;
    renderTag: (configProps: ConfigConsumerProps) => JSX.Element;
    render(): JSX.Element;
}
export default Tag;
