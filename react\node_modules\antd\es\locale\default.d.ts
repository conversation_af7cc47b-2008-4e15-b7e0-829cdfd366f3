declare const _default: {
    locale: string;
    Pagination: any;
    DatePicker: {
        lang: any;
        timePickerLocale: {
            placeholder: string;
        };
    };
    TimePicker: {
        placeholder: string;
    };
    Calendar: {
        lang: any;
        timePickerLocale: {
            placeholder: string;
        };
    };
    global: {
        placeholder: string;
    };
    Table: {
        filterTitle: string;
        filterConfirm: string;
        filterReset: string;
        selectAll: string;
        selectInvert: string;
        sortTitle: string;
        expand: string;
        collapse: string;
    };
    Modal: {
        okText: string;
        cancelText: string;
        justOkText: string;
    };
    Popconfirm: {
        okText: string;
        cancelText: string;
    };
    Transfer: {
        titles: string[];
        searchPlaceholder: string;
        itemUnit: string;
        itemsUnit: string;
    };
    Upload: {
        uploading: string;
        removeFile: string;
        uploadError: string;
        previewFile: string;
        downloadFile: string;
    };
    Empty: {
        description: string;
    };
    Icon: {
        icon: string;
    };
    Text: {
        edit: string;
        copy: string;
        copied: string;
        expand: string;
    };
    PageHeader: {
        back: string;
    };
};
export default _default;
