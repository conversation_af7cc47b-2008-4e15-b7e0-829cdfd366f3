@import '../../style/themes/index';
@import '../../style/mixins/index';

@input-affix-width: 19px;
@input-affix-with-clear-btn-width: 38px;

// size mixins for input
.input-lg() {
  height: @input-height-lg;
  padding: @input-padding-vertical-lg @input-padding-horizontal-lg;
  font-size: @font-size-lg;
}

.input-sm() {
  height: @input-height-sm;
  padding: @input-padding-vertical-sm @input-padding-horizontal-sm;
}

// input status
// == when focus or actived
.active(@color: @outline-color) {
  border-color: ~`colorPalette('@{color}', 5) `;
  border-right-width: @border-width-base !important;
  outline: 0;
  box-shadow: @input-outline-offset @outline-blur-size @outline-width fade(@color, 20%);
}

// == when hoverd
.hover(@color: @input-hover-border-color) {
  border-color: @color;
  border-right-width: @border-width-base !important;
}

.disabled() {
  color: @disabled-color;
  background-color: @input-disabled-bg;
  cursor: not-allowed;
  opacity: 1;

  &:hover {
    .hover(@input-border-color);
  }
}

// Basic style for input
.input() {
  position: relative;
  display: inline-block;
  width: 100%;
  height: @input-height-base;
  padding: @input-padding-vertical-base @input-padding-horizontal-base;
  color: @input-color;
  font-size: @font-size-base;
  line-height: @line-height-base;
  background-color: @input-bg;
  background-image: none;
  border: @border-width-base @border-style-base @input-border-color;
  border-radius: @border-radius-base;
  transition: all 0.3s;
  .placeholder(); // Reset placeholder

  &:hover {
    .hover();
  }

  &:focus {
    .active();
  }

  &-disabled {
    .disabled();
  }

  &[disabled] {
    .disabled();
  }

  // Reset height for `textarea`s
  textarea& {
    max-width: 100%; // prevent textearea resize from coming out of its container
    height: auto;
    min-height: @input-height-base;
    line-height: @line-height-base;
    vertical-align: bottom;
    transition: all 0.3s, height 0s;
  }

  // Size
  &-lg {
    .input-lg();
  }

  &-sm {
    .input-sm();
  }
}

// label input
.input-group(@inputClass) {
  position: relative;
  display: table;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;

  // Undo padding and float of grid classes
  &[class*='col-'] {
    float: none;
    padding-right: 0;
    padding-left: 0;
  }

  > [class*='col-'] {
    padding-right: 8px;

    &:last-child {
      padding-right: 0;
    }
  }

  &-addon,
  &-wrap,
  > .@{inputClass} {
    display: table-cell;

    &:not(:first-child):not(:last-child) {
      border-radius: 0;
    }
  }

  &-addon,
  &-wrap {
    width: 1px; // To make addon/wrap as small as possible
    white-space: nowrap;
    vertical-align: middle;
  }

  &-wrap > * {
    display: block !important;
  }

  .@{inputClass} {
    float: left;
    width: 100%;
    margin-bottom: 0;
    text-align: inherit;

    &:focus {
      z-index: 1; // Fix https://gw.alipayobjects.com/zos/rmsportal/DHNpoqfMXSfrSnlZvhsJ.png
      border-right-width: 1px;
    }

    &:hover {
      z-index: 1;
      border-right-width: 1px;
    }
  }

  &-addon {
    position: relative;
    padding: 0 @input-padding-horizontal-base;
    color: @input-color;
    font-weight: normal;
    font-size: @font-size-base;
    text-align: center;
    background-color: @input-addon-bg;
    border: @border-width-base @border-style-base @input-border-color;
    border-radius: @border-radius-base;
    transition: all 0.3s;

    // Reset Select's style in addon
    .@{ant-prefix}-select {
      margin: -(@input-padding-vertical-base + 1px) (-@input-padding-horizontal-base);

      .@{ant-prefix}-select-selection {
        margin: -1px;
        background-color: inherit;
        border: @border-width-base @border-style-base transparent;
        box-shadow: none;
      }

      &-open,
      &-focused {
        .@{ant-prefix}-select-selection {
          color: @primary-color;
        }
      }
    }

    // Expand addon icon click area
    // https://github.com/ant-design/ant-design/issues/3714
    > i:only-child::after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      content: '';
    }
  }

  // Reset rounded corners
  > .@{inputClass}:first-child,
  &-addon:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;

    // Reset Select's style in addon
    .@{ant-prefix}-select .@{ant-prefix}-select-selection {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  > .@{inputClass}-affix-wrapper {
    &:not(:first-child) .@{inputClass} {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    &:not(:last-child) .@{inputClass} {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  &-addon:first-child {
    border-right: 0;
  }

  &-addon:last-child {
    border-left: 0;
  }

  > .@{inputClass}:last-child,
  &-addon:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;

    // Reset Select's style in addon
    .@{ant-prefix}-select .@{ant-prefix}-select-selection {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  // Sizing options
  &-lg .@{inputClass},
  &-lg > &-addon {
    .input-lg();
  }

  &-sm .@{inputClass},
  &-sm > &-addon {
    .input-sm();
  }

  // Fix https://github.com/ant-design/ant-design/issues/5754
  &-lg .@{ant-prefix}-select-selection--single {
    height: @input-height-lg;
  }

  &-sm .@{ant-prefix}-select-selection--single {
    height: @input-height-sm;
  }

  .@{inputClass}-affix-wrapper {
    display: table-cell;
    float: left;
    width: 100%;
  }

  &&-compact {
    display: block;
    .clearfix;

    &-addon,
    &-wrap,
    > .@{inputClass} {
      &:not(:first-child):not(:last-child) {
        border-right-width: @border-width-base;

        &:hover {
          z-index: 1;
        }

        &:focus {
          z-index: 1;
        }
      }
    }

    & > * {
      display: inline-block;
      float: none;
      vertical-align: top; // https://github.com/ant-design/ant-design-pro/issues/139
      border-radius: 0;
    }

    & > *:not(:last-child) {
      margin-right: -@border-width-base;
      border-right-width: @border-width-base;
    }

    // Undo float for .ant-input-group .ant-input
    .@{inputClass} {
      float: none;
    }

    // reset border for Select, DatePicker, AutoComplete, Cascader, Mention, TimePicker, Input
    & > .@{ant-prefix}-select > .@{ant-prefix}-select-selection,
    & > .@{ant-prefix}-calendar-picker .@{ant-prefix}-input,
    & > .@{ant-prefix}-select-auto-complete .@{ant-prefix}-input,
    & > .@{ant-prefix}-cascader-picker .@{ant-prefix}-input,
    & > .@{ant-prefix}-mention-wrapper .@{ant-prefix}-mention-editor,
    & > .@{ant-prefix}-time-picker .@{ant-prefix}-time-picker-input,
    & > .@{ant-prefix}-input-group-wrapper .@{ant-prefix}-input {
      border-right-width: @border-width-base;
      border-radius: 0;

      &:hover {
        z-index: 1;
      }

      &:focus {
        z-index: 1;
      }
    }

    & > *:first-child,
    & > .@{ant-prefix}-select:first-child > .@{ant-prefix}-select-selection,
    & > .@{ant-prefix}-calendar-picker:first-child .@{ant-prefix}-input,
    & > .@{ant-prefix}-select-auto-complete:first-child .@{ant-prefix}-input,
    & > .@{ant-prefix}-cascader-picker:first-child .@{ant-prefix}-input,
    & > .@{ant-prefix}-mention-wrapper:first-child .@{ant-prefix}-mention-editor,
    & > .@{ant-prefix}-time-picker:first-child .@{ant-prefix}-time-picker-input {
      border-top-left-radius: @border-radius-base;
      border-bottom-left-radius: @border-radius-base;
    }

    & > *:last-child,
    & > .@{ant-prefix}-select:last-child > .@{ant-prefix}-select-selection,
    & > .@{ant-prefix}-calendar-picker:last-child .@{ant-prefix}-input,
    & > .@{ant-prefix}-select-auto-complete:last-child .@{ant-prefix}-input,
    & > .@{ant-prefix}-cascader-picker:last-child .@{ant-prefix}-input,
    & > .@{ant-prefix}-cascader-picker-focused:last-child .@{ant-prefix}-input,
    & > .@{ant-prefix}-mention-wrapper:last-child .@{ant-prefix}-mention-editor,
    & > .@{ant-prefix}-time-picker:last-child .@{ant-prefix}-time-picker-input {
      border-right-width: @border-width-base;
      border-top-right-radius: @border-radius-base;
      border-bottom-right-radius: @border-radius-base;
    }

    // https://github.com/ant-design/ant-design/issues/12493
    & > .@{ant-prefix}-select-auto-complete .@{ant-prefix}-input {
      vertical-align: top;
    }
  }
}

.input-affix-wrapper(@inputClass) {
  position: relative;
  display: inline-block;
  width: 100%;
  text-align: start;

  &:hover .@{inputClass}:not(.@{inputClass}-disabled) {
    .hover();
  }

  .@{inputClass} {
    position: relative;
    text-align: inherit;
  }

  // Should not break align of icon & text
  // https://github.com/ant-design/ant-design/issues/18087
  // https://github.com/ant-design/ant-design/issues/17414
  // https://github.com/ant-design/ant-design/pull/17684
  // https://codesandbox.io/embed/pensive-paper-di2wk
  // https://codesandbox.io/embed/nifty-benz-gb7ml
  .@{inputClass}-prefix,
  .@{inputClass}-suffix {
    position: absolute;
    top: 50%;
    z-index: 2;
    display: flex;
    align-items: center;
    color: @input-color;
    line-height: 0;
    transform: translateY(-50%);

    :not(.anticon) {
      line-height: @line-height-base;
    }
  }

  .@{inputClass}-disabled ~ .@{inputClass}-suffix {
    .anticon {
      color: @disabled-color;
      cursor: not-allowed;
    }
  }

  .@{inputClass}-prefix {
    left: @input-padding-horizontal-base + 1px;
  }

  .@{inputClass}-suffix {
    right: @input-padding-horizontal-base + 1px;
  }

  .@{inputClass}:not(:first-child) {
    padding-left: @input-padding-horizontal-base + @input-affix-width;
  }

  .@{inputClass}:not(:last-child) {
    padding-right: @input-padding-horizontal-base + @input-affix-width;
  }

  &.@{inputClass}-affix-wrapper-input-with-clear-btn .@{inputClass}:not(:last-child) {
    padding-right: @input-padding-horizontal-base + @input-affix-with-clear-btn-width;
  }

  &.@{inputClass}-affix-wrapper-textarea-with-clear-btn .@{inputClass} {
    padding-right: 22px;
  }
}

.clear-icon() {
  color: @disabled-color;
  font-size: @font-size-sm;
  // https://github.com/ant-design/ant-design/pull/18151
  // https://codesandbox.io/s/wizardly-sun-u10br
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: @text-color-secondary;
  }

  &:active {
    color: @text-color;
  }

  + i {
    margin-left: 6px;
  }
}
