declare const _default: {
    locale: string;
    Pagination: any;
    DatePicker: {
        lang: any;
        timePickerLocale: {
            placeholder: string;
        };
    };
    TimePicker: {
        placeholder: string;
    };
    Calendar: {
        lang: any;
        timePickerLocale: {
            placeholder: string;
        };
    };
    global: {
        placeholder: string;
    };
    PageHeader: {
        back: string;
    };
    Text: {
        edit: string;
        copy: string;
        copied: string;
        expand: string;
    };
    Empty: {
        description: string;
    };
    Table: {
        filterTitle: string;
        filterConfirm: string;
        filterReset: string;
        emptyText: string;
        selectAll: string;
        selectInvert: string;
    };
    Modal: {
        okText: string;
        cancelText: string;
        justOkText: string;
    };
    Popconfirm: {
        okText: string;
        cancelText: string;
    };
    Transfer: {
        notFoundContent: string;
        searchPlaceholder: string;
        itemUnit: string;
        itemsUnit: string;
    };
    Icon: {
        icon: string;
    };
    Select: {
        notFoundContent: string;
    };
    Upload: {
        uploading: string;
        removeFile: string;
        uploadError: string;
        previewFile: string;
        downloadFile: string;
    };
};
export default _default;
