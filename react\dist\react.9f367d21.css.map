{"mappings": "ACAA;;;;;;;;;ACAA;;;;AAIA;;;;;;;;;AASA;;;;;;;AAOA;;;;;AAKA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;;AAQA", "sources": ["b9e8aa229f367d21", "src/App.css", "src/base.css"], "sourcesContent": ["@import \"4a35310afba245da\";\n@import \"dde3864534ca93dc\";\n", "html {\n  color-scheme: light dark;\n  font-family: system-ui;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n}\n", "* {\r\n  transition: 0.4s ease-out;\r\n}\r\n\r\n.process-building {\r\n  width: max-content;\r\n  padding: 20px;\r\n  background-color: #eee;\r\n  display: grid;\r\n  grid-template-columns: 240px 1fr;\r\n  gap: 20px;\r\n}\r\n\r\n.dir {\r\n  padding: 12px;\r\n  border: 1px solid cyan;\r\n  user-select: none;\r\n  font-size: 16px;\r\n}\r\n\r\n.dir > div {\r\n  cursor: pointer;\r\n  height: 32px;\r\n}\r\n\r\n.dir ul {\r\n  margin: 0;\r\n  transition: max-height 0.3s ease-out;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n}\r\n\r\n.dir .title {\r\n  display: grid;\r\n  grid-template-columns: 20px 1fr;\r\n  align-items: center;\r\n}\r\n\r\n.dir .arrow-container {\r\n  display: grid;\r\n  place-content: center;\r\n}\r\n\r\n.content {\r\n  padding: 12px 20px;\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  grid-auto-rows: max-content;\r\n  gap: 20px;\r\n}\r\n\r\n.content > div {\r\n  padding: 8px;\r\n  border: 1px solid #00d7d5;\r\n}\r\n"], "names": [], "version": 3, "file": "react.9f367d21.css.map", "sourceRoot": "/__parcel_source_root/"}