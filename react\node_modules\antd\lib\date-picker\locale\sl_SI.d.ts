declare const locale: {
    lang: {
        placeholder: string;
        rangePlaceholder: string[];
        today: string;
        now: string;
        backToToday: string;
        ok: string;
        clear: string;
        month: string;
        year: string;
        timeSelect: string;
        dateSelect: string;
        monthSelect: string;
        yearSelect: string;
        decadeSelect: string;
        yearFormat: string;
        dateFormat: string;
        dayFormat: string;
        dateTimeFormat: string;
        monthFormat: string;
        monthBeforeYear: boolean;
        previousMonth: string;
        nextMonth: string;
        previousYear: string;
        nextYear: string;
        previousDecade: string;
        nextDecade: string;
        previousCentury: string;
        nextCentury: string;
    };
    timePickerLocale: {
        placeholder: string;
    };
};
export default locale;
