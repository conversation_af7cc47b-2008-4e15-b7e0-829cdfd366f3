declare const _default: {
    locale: string;
    Pagination: any;
    DatePicker: {
        lang: {
            placeholder: string;
            rangePlaceholder: string[];
            today: string;
            now: string;
            backToToday: string;
            ok: string;
            clear: string;
            month: string;
            year: string;
            timeSelect: string;
            dateSelect: string;
            monthSelect: string;
            yearSelect: string;
            decadeSelect: string;
            yearFormat: string;
            dateFormat: string;
            dayFormat: string;
            dateTimeFormat: string;
            monthFormat: string;
            monthBeforeYear: boolean;
            previousMonth: string;
            nextMonth: string;
            previousYear: string;
            nextYear: string;
            previousDecade: string;
            nextDecade: string;
            previousCentury: string;
            nextCentury: string;
        };
        timePickerLocale: {
            placeholder: string;
        };
    };
    TimePicker: {
        placeholder: string;
    };
    Calendar: {
        lang: {
            placeholder: string;
            rangePlaceholder: string[];
            today: string;
            now: string;
            backToToday: string;
            ok: string;
            clear: string;
            month: string;
            year: string;
            timeSelect: string;
            dateSelect: string;
            monthSelect: string;
            yearSelect: string;
            decadeSelect: string;
            yearFormat: string;
            dateFormat: string;
            dayFormat: string;
            dateTimeFormat: string;
            monthFormat: string;
            monthBeforeYear: boolean;
            previousMonth: string;
            nextMonth: string;
            previousYear: string;
            nextYear: string;
            previousDecade: string;
            nextDecade: string;
            previousCentury: string;
            nextCentury: string;
        };
        timePickerLocale: {
            placeholder: string;
        };
    };
    Table: {
        filterTitle: string;
        filterConfirm: string;
        filterReset: string;
        selectAll: string;
        selectInvert: string;
    };
    Modal: {
        okText: string;
        cancelText: string;
        justOkText: string;
    };
    Popconfirm: {
        okText: string;
        cancelText: string;
    };
    Transfer: {
        searchPlaceholder: string;
        itemUnit: string;
        itemsUnit: string;
    };
    Upload: {
        uploading: string;
        removeFile: string;
        uploadError: string;
        previewFile: string;
        downloadFile: string;
    };
    Empty: {
        description: string;
    };
};
export default _default;
