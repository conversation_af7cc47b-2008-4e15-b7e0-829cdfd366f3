* {
  transition: 0.4s ease-out;
}

.process-building {
  width: max-content;
  padding: 20px;
  background-color: #eee;
  display: grid;
  grid-template-columns: 240px 1fr;
  gap: 20px;
}

.dir {
  padding: 12px;
  border: 1px solid cyan;
  user-select: none;
  font-size: 16px;
}

.dir > div {
  cursor: pointer;
  height: 32px;
}

.dir ul {
  margin: 0;
  transition: max-height 0.3s ease-out;
  overflow: hidden;
  cursor: pointer;
}

.dir .title {
  display: grid;
  grid-template-columns: 20px 1fr;
  align-items: center;
}

.dir .arrow-container {
  display: grid;
  place-content: center;
}

.content {
  padding: 12px 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: max-content;
  gap: 20px;
}

.content > div {
  padding: 8px;
  border: 1px solid #00d7d5;
}
