html {
  color-scheme: light dark;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-family: system-ui;
  display: flex;
}

* {
  transition: all .4s ease-out;
}

.process-building {
  background-color: #eee;
  grid-template-columns: 240px 1fr;
  gap: 20px;
  width: max-content;
  padding: 20px;
  display: grid;
}

.dir {
  user-select: none;
  border: 1px solid #0ff;
  padding: 12px;
  font-size: 16px;
}

.dir > div {
  cursor: pointer;
  height: 32px;
}

.dir ul {
  cursor: pointer;
  margin: 0;
  transition: max-height .3s ease-out;
  overflow: hidden;
}

.dir .title {
  grid-template-columns: 20px 1fr;
  align-items: center;
  display: grid;
}

.dir .arrow-container {
  place-content: center;
  display: grid;
}

.content {
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: max-content;
  gap: 20px;
  padding: 12px 20px;
  display: grid;
}

.content > div {
  border: 1px solid #00d7d5;
  padding: 8px;
}
/*# sourceMappingURL=react.9f367d21.css.map */
