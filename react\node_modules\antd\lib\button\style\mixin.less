// mixins for button
// ------------------------
.button-size(@height; @padding; @font-size; @border-radius) {
  height: @height;
  padding: @padding;
  font-size: @font-size;
  border-radius: @border-radius;
}

.button-disabled(@color: @btn-disable-color; @background: @btn-disable-bg; @border: @btn-disable-border) {
  &-disabled,
  &.disabled,
  &[disabled] {
    &,
    &:hover,
    &:focus,
    &:active,
    &.active {
      .button-color(@color; @background; @border);

      text-shadow: none;
      box-shadow: none;
    }
  }
}

.button-variant-primary(@color; @background) {
  .button-color(@color; @background; @background);

  text-shadow: @btn-text-shadow;
  box-shadow: @btn-primary-shadow;

  &:hover,
  &:focus {
    .button-color(
      @color; ~`colorPalette('@{background}', 5) `; ~`colorPalette('@{background}', 5) `
    );
  }

  &:active,
  &.active {
    .button-color(
      @color; ~`colorPalette('@{background}', 7) `; ~`colorPalette('@{background}', 7) `
    );
  }

  .button-disabled();
}

.button-variant-other(@color; @background; @border) {
  .button-color(@color; @background; @border);

  &:hover,
  &:focus {
    .button-color(
      ~`colorPalette('@{btn-primary-bg}', 5) `; @background; ~`colorPalette('@{btn-primary-bg}', 5)
        `
    );
  }
  &:active,
  &.active {
    .button-color(
      ~`colorPalette('@{btn-primary-bg}', 7) `; @background; ~`colorPalette('@{btn-primary-bg}', 7)
        `
    );
  }
  .button-disabled();
}
.button-variant-ghost(@color; @border: @color) {
  .button-color(@color; transparent; @border);
  text-shadow: none;
  &:hover,
  &:focus {
    & when (@border = transparent) {
      .button-color(~`colorPalette('@{color}', 5) `; transparent; transparent);
    }
    & when not(@border = transparent) {
      .button-color(~`colorPalette('@{color}', 5) `; transparent; ~`colorPalette('@{color}', 5) `);
    }
  }
  &:active,
  &.active {
    & when (@border = transparent) {
      .button-color(~`colorPalette('@{color}', 7) `; transparent; transparent);
    }
    & when not(@border = transparent) {
      .button-color(~`colorPalette('@{color}', 7) `; transparent; ~`colorPalette('@{color}', 7) `);
    }
  }
  .button-disabled();
}
.button-color(@color; @background; @border) {
  color: @color;
  background-color: @background;
  border-color: @border;
  // a inside Button which only work in Chrome
  // http://stackoverflow.com/a/17253457
  > a:only-child {
    color: currentColor;
    &::after {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      background: transparent;
      content: '';
    }
  }
}
.button-group-base(@btnClassName) {
  position: relative;
  display: inline-block;
  > .@{btnClassName},
  > span > .@{btnClassName} {
    position: relative;
    &:hover,
    &:focus,
    &:active,
    &.active {
      z-index: 2;
    }
    &:disabled {
      z-index: 0;
    }
  }
  > .@{btnClassName}-icon-only {
    font-size: @font-size-base;
  }
  // size
  &-lg > .@{btnClassName},
  &-lg > span > .@{btnClassName} {
    .button-size(@btn-height-lg; @btn-padding-lg; @btn-font-size-lg; 0);
    line-height: @btn-height-lg - 2px;
  }
  &-lg > .@{btnClassName}.@{btnClassName}-icon-only {
    .square(@btn-height-lg);
    padding-right: 0;
    padding-left: 0;
  }
  &-sm > .@{btnClassName},
  &-sm > span > .@{btnClassName} {
    .button-size(@btn-height-sm; @btn-padding-sm; @font-size-base; 0);
    line-height: @btn-height-sm - 2px;
    > .@{iconfont-css-prefix} {
      font-size: @font-size-base;
    }
  }
  &-sm > .@{btnClassName}.@{btnClassName}-icon-only {
    .square(@btn-height-sm);
    padding-right: 0;
    padding-left: 0;
  }
}
// Base styles of buttons
// --------------------------------------------------
.btn() {
  position: relative;
  display: inline-block;
  font-weight: @btn-font-weight;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  border: @btn-border-width @btn-border-style transparent;
  box-shadow: @btn-shadow;
  cursor: pointer;
  transition: all 0.3s @ease-in-out;
  user-select: none;
  touch-action: manipulation;
  .button-size(@btn-height-base; @btn-padding-base; @font-size-base; @btn-border-radius-base);
  > .@{iconfont-css-prefix} {
    line-height: 1;
  }
  &,
  &:active,
  &:focus {
    outline: 0;
  }
  &:not([disabled]):hover {
    text-decoration: none;
  }
  &:not([disabled]):active {
    outline: 0;
    box-shadow: none;
  }
  &.disabled,
  &[disabled] {
    cursor: not-allowed;
    > * {
      pointer-events: none;
    }
  }
  &-lg {
    .button-size(@btn-height-lg; @btn-padding-lg; @btn-font-size-lg; @btn-border-radius-base);
  }
  &-sm {
    .button-size(@btn-height-sm; @btn-padding-sm; @btn-font-size-sm; @btn-border-radius-sm);
  }
}
// primary button style
.btn-primary() {
  .button-variant-primary(@btn-primary-color; @btn-primary-bg);
}
// default button style
.btn-default() {
  .button-variant-other(@btn-default-color; @btn-default-bg; @btn-default-border);
  &:hover,
  &:focus,
  &:active,
  &.active {
    text-decoration: none;
    background: @btn-default-bg;
  }
}
// ghost button style
.btn-ghost() {
  .button-variant-other(@btn-ghost-color, @btn-ghost-bg, @btn-ghost-border);
}
// dashed button style
.btn-dashed() {
  .button-variant-other(@btn-default-color, @btn-default-bg, @btn-default-border);
  border-style: dashed;
}
// danger button style
.btn-danger() {
  .button-variant-primary(@btn-danger-color, @btn-danger-bg);
}
// link button style
.btn-link() {
  .button-variant-other(@link-color, transparent, transparent);
  box-shadow: none;
  &:hover,
  &:focus,
  &:active {
    border-color: transparent;
  }
  .button-disabled(@disabled-color; transparent; transparent);
}
// round button
.btn-round(@btnClassName: btn) {
  .button-size(@btn-circle-size; 0 @btn-circle-size / 2; @font-size-base; @btn-circle-size);
  &.@{btnClassName}-lg {
    .button-size(
      @btn-circle-size-lg; 0 @btn-circle-size-lg / 2; @btn-font-size-lg; @btn-circle-size-lg
    );
  }
  &.@{btnClassName}-sm {
    .button-size(
      @btn-circle-size-sm; 0 @btn-circle-size-sm / 2; @font-size-base; @btn-circle-size-sm
    );
  }
}
// square button: the content only contains icon
.btn-square(@btnClassName: btn) {
  .square(@btn-square-size);
  .button-size(@btn-square-size; 0; @font-size-base + 2px; @btn-border-radius-base);
  &.@{btnClassName}-lg {
    .square(@btn-square-size-lg);
    .button-size(@btn-square-size-lg; 0; @btn-font-size-lg + 2px; @btn-border-radius-base);
  }
  &.@{btnClassName}-sm {
    .square(@btn-square-size-sm);
    .button-size(@btn-square-size-sm; 0; @font-size-base; @btn-border-radius-base);
  }
}
// circle button: the content only contains icon
.btn-circle(@btnClassName: btn) {
  min-width: @btn-height-base;
  padding-right: 0;
  padding-left: 0;
  text-align: center;
  border-radius: 50%;
  &.@{btnClassName}-lg {
    min-width: @btn-height-lg;
    border-radius: 50%;
  }
  &.@{btnClassName}-sm {
    min-width: @btn-height-sm;
    border-radius: 50%;
  }
}
// Horizontal button groups style
// --------------------------------------------------
.btn-group(@btnClassName: btn) {
  .button-group-base(@btnClassName);
  .@{btnClassName} + .@{btnClassName},
  .@{btnClassName} + &,
  span + .@{btnClassName},
  .@{btnClassName} + span,
  > span + span,
  & + .@{btnClassName},
  & + & {
    margin-left: -1px;
  }
  .@{btnClassName}-primary + .@{btnClassName}:not(.@{btnClassName}-primary):not([disabled]) {
    border-left-color: transparent;
  }
  .@{btnClassName} {
    border-radius: 0;
  }
  > .@{btnClassName}:first-child,
  > span:first-child > .@{btnClassName} {
    margin-left: 0;
  }
  > .@{btnClassName}:only-child {
    border-radius: @btn-border-radius-base;
  }
  > span:only-child > .@{btnClassName} {
    border-radius: @btn-border-radius-base;
  }
  > .@{btnClassName}:first-child:not(:last-child),
  > span:first-child:not(:last-child) > .@{btnClassName} {
    border-top-left-radius: @btn-border-radius-base;
    border-bottom-left-radius: @btn-border-radius-base;
  }
  > .@{btnClassName}:last-child:not(:first-child),
  > span:last-child:not(:first-child) > .@{btnClassName} {
    border-top-right-radius: @btn-border-radius-base;
    border-bottom-right-radius: @btn-border-radius-base;
  }
  &-sm {
    > .@{btnClassName}:only-child {
      border-radius: @btn-border-radius-sm;
    }
    > span:only-child > .@{btnClassName} {
      border-radius: @btn-border-radius-sm;
    }
    > .@{btnClassName}:first-child:not(:last-child),
    > span:first-child:not(:last-child) > .@{btnClassName} {
      border-top-left-radius: @btn-border-radius-sm;
      border-bottom-left-radius: @btn-border-radius-sm;
    }
    > .@{btnClassName}:last-child:not(:first-child),
    > span:last-child:not(:first-child) > .@{btnClassName} {
      border-top-right-radius: @btn-border-radius-sm;
      border-bottom-right-radius: @btn-border-radius-sm;
    }
  }
  & > & {
    float: left;
  }
  & > &:not(:first-child):not(:last-child) > .@{btnClassName} {
    border-radius: 0;
  }
  & > &:first-child:not(:last-child) {
    > .@{btnClassName}:last-child {
      padding-right: 8px;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
  & > &:last-child:not(:first-child) > .@{btnClassName}:first-child {
    padding-left: 8px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
