@dialog-prefix-cls: ~'@{ant-prefix}-modal';
@table-prefix-cls: ~'@{ant-prefix}-table';
@modal-footer-padding-vertical: 10px;
@modal-footer-padding-horizontal: 16px;

.@{dialog-prefix-cls} {
  .reset-component;

  position: relative;
  top: 100px;
  width: auto;
  margin: 0 auto;
  padding-bottom: 24px;
  pointer-events: none;

  &-wrap {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: @zindex-modal;
    overflow: auto;
    outline: 0;
    -webkit-overflow-scrolling: touch;
  }

  &-title {
    margin: 0;
    color: @modal-heading-color;
    font-weight: 500;
    font-size: @font-size-lg;
    line-height: 22px;
    word-wrap: break-word;
  }

  &-content {
    position: relative;
    background-color: @component-background;
    background-clip: padding-box;
    border: 0;
    border-radius: @border-radius-base;
    box-shadow: @shadow-2;
    pointer-events: auto;
  }

  &-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: @zindex-popup-close;
    padding: 0;
    color: @text-color-secondary;
    font-weight: 700;
    line-height: 1;
    text-decoration: none;
    background: transparent;
    border: 0;
    outline: 0;
    cursor: pointer;
    transition: color 0.3s;

    &-x {
      display: block;
      width: 56px;
      height: 56px;
      font-size: @font-size-lg;
      font-style: normal;
      line-height: 56px;
      text-align: center;
      text-transform: none;
      text-rendering: auto;
    }

    &:focus,
    &:hover {
      color: @icon-color-hover;
      text-decoration: none;
    }
  }

  &-header {
    padding: 16px 24px;
    color: @text-color;
    background: @modal-header-bg;
    border-bottom: @border-width-base @border-style-base @border-color-split;
    border-radius: @border-radius-base @border-radius-base 0 0;
  }

  &-body {
    padding: @modal-body-padding;
    font-size: @font-size-base;
    line-height: @line-height-base;
    word-wrap: break-word;
  }

  &-footer {
    padding: @modal-footer-padding-vertical @modal-footer-padding-horizontal;
    text-align: right;
    background: @modal-footer-bg;
    border-top: @border-width-base @border-style-base @modal-footer-border-color-split;
    border-radius: 0 0 @border-radius-base @border-radius-base;
    button + button {
      margin-bottom: 0;
      margin-left: 8px;
    }
  }

  &.zoom-enter,
  &.zoom-appear {
    transform: none; // reset scale avoid mousePosition bug
    opacity: 0;
    animation-duration: @animation-duration-slow;
    user-select: none; // https://github.com/ant-design/ant-design/issues/11777
  }

  &-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: @zindex-modal-mask;
    height: 100%;
    background-color: @modal-mask-bg;
    filter: ~'alpha(opacity=50)';

    &-hidden {
      display: none;
    }
  }

  &-open {
    overflow: hidden;
  }
}

.@{dialog-prefix-cls}-centered {
  text-align: center;
  &::before {
    display: inline-block;
    width: 0;
    height: 100%;
    vertical-align: middle;
    content: '';
  }
  .@{dialog-prefix-cls} {
    top: 0;
    display: inline-block;
    text-align: left;
    vertical-align: middle;
  }
}

@media (max-width: @screen-sm-max) {
  .@{dialog-prefix-cls} {
    max-width: calc(100vw - 16px);
    margin: 8px auto;
  }
  .@{dialog-prefix-cls}-centered {
    .@{dialog-prefix-cls} {
      flex: 1;
    }
  }
}
